'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { httpsCallable } from 'firebase/functions';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import * as z from 'zod';

import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { useToast } from '@/hooks/use-toast';
import { firebaseFunctions, useRootContext } from '@/root-context';
import { consumeReferralId } from '@/utils/referral-utils';

const profileSchema = z.object({
  name: z.string().optional(),
  tg_id: z.string().optional(),
  ton_wallet_address: z.string().optional(),
});

type ProfileFormData = z.infer<typeof profileSchema>;

export const ProfileUpdatePage = () => {
  const { currentUser, refetchUser } = useRootContext();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<ProfileFormData>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      name: '',
      tg_id: '',
      ton_wallet_address: '',
    },
  });

  // Load user data on component mount
  useEffect(() => {
    if (currentUser) {
      // For now, we'll use the Firebase user data as a starting point
      // In a real implementation, you'd fetch the UserEntity from your database
      form.setValue('name', currentUser.displayName ?? '');
      // tg_id and ton_wallet_address would come from your database
    }
  }, [currentUser, form]);

  const onSubmit = async (data: ProfileFormData) => {
    if (!currentUser) {
      toast({
        title: 'Error',
        description: 'You must be logged in to update your profile.',
        variant: 'destructive',
      });
      return;
    }

    setIsLoading(true);

    try {
      // Get referral ID from localStorage if available
      const referralId = consumeReferralId();

      // Call the Firebase callable function
      const changeUserData = httpsCallable(firebaseFunctions, 'changeUserData');

      const result = await changeUserData({
        name: data.name,
        tg_id: data.tg_id,
        ton_wallet_address: data.ton_wallet_address,
        referral_id: referralId, // Include referral ID if available
      });

      toast({
        title: 'Success',
        description: 'Your profile has been updated successfully.',
      });

      console.log('Profile updated:', result.data);

      // Refetch user data to get updated profile information
      await refetchUser();
    } catch (error) {
      console.error('Error updating profile:', error);
      toast({
        title: 'Error',
        description: 'Failed to update your profile. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (!currentUser) {
    return (
      <div className="container mx-auto py-8">
        <Card className="max-w-md mx-auto">
          <CardHeader>
            <CardTitle>Access Denied</CardTitle>
            <CardDescription>
              You must be logged in to access your profile.
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      <Card className="max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle>Update Profile</CardTitle>
          <CardDescription>
            Update your personal information and wallet details.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter your name" {...field} />
                    </FormControl>
                    <FormDescription>
                      Your display name that will be shown to other users.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="tg_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Telegram ID</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter your Telegram ID" {...field} />
                    </FormControl>
                    <FormDescription>
                      Your Telegram user ID for notifications and communication.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="ton_wallet_address"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>TON Wallet Address</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter your TON wallet address"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Your TON blockchain wallet address for transactions.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="flex gap-4">
                <Button type="submit" disabled={isLoading} className="flex-1">
                  {isLoading ? 'Updating...' : 'Update Profile'}
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => form.reset()}
                  disabled={isLoading}
                >
                  Reset
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
};
