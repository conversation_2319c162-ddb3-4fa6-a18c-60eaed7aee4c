'use client';

import { useEffect } from 'react';

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Role } from '@/core.constants';
import { useRootContext } from '@/root-context';

import { CollectionManagement } from './collection-management';
import { CustomReferralManagement } from './custom-referral-management';
import { FeesManagement } from './fees-management';
import { OrderStatsDisplay } from './order-stats-display';
import { RevenueDisplay } from './revenue-display';
import { StarsCountDisplay } from './stars-count-display';

export default function Admin() {
  const { role } = useRootContext();

  useEffect(() => {
    document.documentElement.classList.add('dark');
  }, []);

  if (role !== Role.ADMIN) return null;

  return (
    <div className="mx-auto w-full max-w-6xl p-4">
      <Tabs defaultValue="revenue" className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="revenue">Revenue & Fees</TabsTrigger>
          <TabsTrigger value="collections">Collections</TabsTrigger>
          <TabsTrigger value="referrals">Custom Referrals</TabsTrigger>
          <TabsTrigger value="stats">Stats</TabsTrigger>
        </TabsList>

        <TabsContent value="revenue" className="mt-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <RevenueDisplay />
            <FeesManagement />
            <StarsCountDisplay />
          </div>
        </TabsContent>

        <TabsContent value="referrals" className="mt-6">
          <CustomReferralManagement />
        </TabsContent>

        <TabsContent value="collections" className="mt-6">
          <CollectionManagement />
        </TabsContent>

        <TabsContent value="stats" className="mt-6">
          <OrderStatsDisplay />
        </TabsContent>
      </Tabs>
    </div>
  );
}
