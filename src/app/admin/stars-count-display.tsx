'use client';

import { RefreshCw, Star } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';

import { getPaidOrdersCount } from '@/api/order-api';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';

const STARS_PER_ORDER = 25;

export const StarsCountDisplay = () => {
  const { toast } = useToast();
  const [paidOrdersCount, setPaidOrdersCount] = useState<number | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);

  const loadPaidOrdersCount = useCallback(async () => {
    try {
      setIsLoading(true);
      const count = await getPaidOrdersCount();
      setPaidOrdersCount(count);
    } catch (error) {
      console.error('Error loading paid orders count:', error);
      toast({
        title: 'Error',
        description: 'Failed to load paid orders count',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  const handleRefresh = async () => {
    try {
      setIsRefreshing(true);
      await loadPaidOrdersCount();
      toast({
        title: 'Success',
        description: 'Stars count refreshed successfully',
      });
    } catch (error) {
      console.error('Error refreshing stars count:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  useEffect(() => {
    loadPaidOrdersCount();
  }, [loadPaidOrdersCount]);

  const requiredStars =
    paidOrdersCount !== null ? paidOrdersCount * STARS_PER_ORDER : 0;

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-lg">
          <Star className="h-4 w-4" />
          Relayer Stars Requirement
        </CardTitle>
        <CardDescription className="text-sm">
          Minimum stars needed on relayer for sending gifts
        </CardDescription>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Paid Orders:</span>
            <span className="text-lg font-bold">
              {isLoading
                ? 'Loading...'
                : (paidOrdersCount?.toLocaleString() ?? '0')}
            </span>
          </div>

          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Stars per Order:</span>
            <span className="text-lg font-bold">{STARS_PER_ORDER}</span>
          </div>

          <div className="border-t pt-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Required Stars:</span>
              <span className="text-xl font-bold text-primary">
                {isLoading ? 'Loading...' : requiredStars.toLocaleString()}
              </span>
            </div>
          </div>

          <div className="bg-muted p-3 rounded-md">
            <p className="text-sm text-muted-foreground">
              Minimum required number of stars on relayer to be needed for
              sending gifts:{' '}
              <span className="font-semibold text-foreground">
                {paidOrdersCount?.toLocaleString() ?? '0'} × {STARS_PER_ORDER} ={' '}
                {requiredStars.toLocaleString()}
              </span>
            </p>
          </div>

          <Button
            onClick={handleRefresh}
            disabled={isRefreshing}
            variant="outline"
            size="sm"
            className="w-full"
          >
            <RefreshCw
              className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`}
            />
            {isRefreshing ? 'Refreshing...' : 'Refresh Count'}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
