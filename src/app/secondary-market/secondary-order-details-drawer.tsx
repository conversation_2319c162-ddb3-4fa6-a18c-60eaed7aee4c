'use client';

import { Caption } from '@telegram-apps/telegram-ui';
import { Loader2, User } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';
import { toast } from 'sonner';
import { Drawer } from 'vaul';

import { getUserById } from '@/api/auth-api';
import { makeSecondaryMarketPurchase } from '@/api/secondary-market-orders-api';
import { TgsOrImage } from '@/components/TgsOrImage';
import { TonLogo } from '@/components/TonLogo';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import type { OrderEntity, UserEntity } from '@/core.constants';
import { useRootContext } from '@/root-context';

interface SecondaryOrderDetailsDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  order: OrderEntity | null;
  onOrderAction?: () => void;
}

export function SecondaryOrderDetailsDrawer({
  open,
  onOpenChange,
  order,
  onOrderAction,
}: SecondaryOrderDetailsDrawerProps) {
  const { collections } = useRootContext();
  const [sellerInfo, setSellerInfo] = useState<UserEntity | null>(null);
  const [loading, setLoading] = useState(false);
  const [actionLoading, setActionLoading] = useState(false);

  const collection = order
    ? collections.find((c) => c.id === order.collectionId)
    : null;

  const loadSellerInfo = useCallback(async () => {
    if (!order?.sellerId) return;

    setLoading(true);
    try {
      const user = await getUserById(order.sellerId);
      setSellerInfo(user);
    } catch (error) {
      console.error('Error loading seller info:', error);
      setSellerInfo(null);
    } finally {
      setLoading(false);
    }
  }, [order?.sellerId]);

  useEffect(() => {
    if (open && order?.sellerId) {
      loadSellerInfo();
    }
  }, [open, order?.sellerId, loadSellerInfo]);

  const handlePurchase = async () => {
    if (!order?.id) return;

    setActionLoading(true);
    try {
      const result = await makeSecondaryMarketPurchase(order.id);

      const message =
        result.message || 'Secondary market purchase completed successfully!';
      toast.success(message);
      onOpenChange(false);

      if (onOrderAction) {
        onOrderAction();
      }
    } catch (error: unknown) {
      console.error('Secondary market purchase failed:', error);
      const errorMessage =
        error instanceof Error
          ? error.message
          : 'Purchase failed. Please try again.';
      toast.error(errorMessage);
    } finally {
      setActionLoading(false);
    }
  };

  const handleClose = () => {
    setSellerInfo(null);
    onOpenChange(false);
  };

  if (!order) return null;

  return (
    <Drawer.Root
      open={open}
      onOpenChange={onOpenChange}
      shouldScaleBackground
      modal={true}
      dismissible={true}
    >
      <Drawer.Portal>
        <Drawer.Title />
        <Drawer.Overlay className="fixed inset-0 bg-black/40 z-[100]" />
        <Drawer.Content className="bg-[#17212b] flex flex-col rounded-t-[20px] mt-16 fixed bottom-0 left-0 right-0 z-[101] outline-none focus:outline-none">
          <div className="p-6 bg-[#17212b] rounded-t-[20px] flex-1 max-h-[85vh] overflow-y-auto">
            <div className="mx-auto w-12 h-1.5 flex-shrink-0 rounded-full bg-[#708499] mb-6 cursor-grab active:cursor-grabbing touch-manipulation" />

            <div className="max-w-md mx-auto space-y-6">
              <div className="relative">
                <div className="aspect-square relative rounded-2xl overflow-hidden bg-gradient-to-br from-[#232e3c] to-[#1a252f] p-8 border border-[#3a4a5c]/50">
                  <TgsOrImage
                    isImage={false}
                    collectionId={order.collectionId}
                    imageProps={{
                      alt: collection?.name || 'Order item',
                      fill: true,
                      className: 'object-contain drop-shadow-2xl',
                    }}
                    tgsProps={{
                      style: { height: '100%', width: '100%' },
                    }}
                  />
                </div>
              </div>

              <div className="text-center space-y-2">
                <h1 className="text-2xl font-bold text-[#f5f5f5]">
                  {collection?.name || 'Unknown Collection'}
                </h1>
                <Caption level="1" weight="3" className="text-[#708499]">
                  #
                  {order.number ||
                    (typeof order.id === 'string'
                      ? order.id?.slice(-6)
                      : 'N/A')}
                </Caption>
              </div>

              <div className="space-y-4">
                {collection?.description && (
                  <div className="text-center">
                    <Caption level="2" weight="3" className="text-[#708499]">
                      {collection.description}
                    </Caption>
                  </div>
                )}

                <div className="space-y-3">
                  <div className="flex justify-between items-center py-2 border-b border-[#3a4a5c]/30">
                    <span className="text-[#f5f5f5] font-medium">
                      Primary Price
                    </span>
                    <div className="flex items-center gap-1">
                      <span className="text-[#6ab2f2] font-semibold">
                        {order.amount}
                      </span>
                      <TonLogo size={16} />
                    </div>
                  </div>

                  <div className="flex justify-between items-center py-2 border-b border-[#3a4a5c]/30">
                    <span className="text-[#f5f5f5] font-medium">
                      Secondary Price
                    </span>
                    <div className="flex items-center gap-1">
                      <span className="text-[#6ab2f2] font-semibold">
                        {order.secondaryMarketPrice || 0}
                      </span>
                      <TonLogo size={16} className="text-[#6ab2f2]" />
                    </div>
                  </div>

                  <div className="flex justify-between items-center py-2 border-b border-[#3a4a5c]/30">
                    <span className="text-[#f5f5f5] font-medium">Status</span>
                    <Badge
                      variant="secondary"
                      className="bg-[#6ab2f2]/20 text-[#6ab2f2] border-[#6ab2f2]/30"
                    >
                      Secondary Market
                    </Badge>
                  </div>

                  <div className="flex justify-between items-center py-2">
                    <span className="text-[#f5f5f5] font-medium">
                      Market Type
                    </span>
                    <span className="text-[#6ab2f2] font-semibold">Resale</span>
                  </div>
                </div>
              </div>

              {order.sellerId && (
                <div className="bg-[#232e3c]/50 rounded-2xl p-4 border border-[#3a4a5c]/30 flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-[#6ab2f2] rounded-full flex items-center justify-center">
                      <User className="w-4 h-4 text-white" />
                    </div>
                    <span className="text-[#f5f5f5] font-medium">
                      Current Owner
                    </span>
                  </div>

                  {loading ? (
                    <div className="flex items-center gap-2 text-[#708499]">
                      <Loader2 className="w-4 h-4 animate-spin" />
                      <Caption level="2" weight="3">
                        Loading...
                      </Caption>
                    </div>
                  ) : sellerInfo ? (
                    <div className="space-y-1">
                      <p className="font-medium text-[#f5f5f5]">
                        {sellerInfo.displayName ||
                          sellerInfo.email ||
                          'Anonymous User'}
                      </p>
                      {sellerInfo.email && sellerInfo.displayName && (
                        <Caption
                          level="2"
                          weight="3"
                          className="text-[#708499]"
                        >
                          {sellerInfo.email}
                        </Caption>
                      )}
                    </div>
                  ) : (
                    <Caption level="2" weight="3" className="text-[#708499]">
                      User information not available
                    </Caption>
                  )}
                </div>
              )}

              <div className="space-y-3 pt-4">
                <Button
                  onClick={handlePurchase}
                  disabled={actionLoading}
                  className="w-full h-12 bg-[#6ab2f2] hover:bg-[#6ab2f2]/90 text-white border-0 rounded-2xl"
                >
                  {actionLoading ? (
                    <>
                      <Loader2 className="w-5 h-5 animate-spin mr-2" />
                      Processing...
                    </>
                  ) : (
                    <>
                      Buy &#40;{order.secondaryMarketPrice || 0}{' '}
                      <TonLogo className="-m-2" size={18} />
                      <span className="-ml-1 translate-x-[1px]">&#41;</span>
                    </>
                  )}
                </Button>

                <Button
                  variant="outline"
                  onClick={handleClose}
                  className="w-full h-12 border-[#3a4a5c] text-[#f5f5f5] hover:bg-[#232e3c]/50 bg-transparent rounded-2xl"
                  disabled={actionLoading}
                >
                  Close
                </Button>
              </div>
            </div>
          </div>
        </Drawer.Content>
      </Drawer.Portal>
    </Drawer.Root>
  );
}
