'use client';

import { GridItem } from '@/components/ui/virtualized-grid';
import type { Collection, OrderEntity } from '@/core.constants';

import { OrderCard } from './order-card';

interface VirtualizedOrderCardProps {
  order: OrderEntity;
  collection: Collection | undefined;
  onClick: () => void;
  animated?: boolean;
  index: number;
  initialRenderedCount?: number;
}

export const VirtualizedOrderCard = ({
  order,
  collection,
  onClick,
  animated,
  index,
  initialRenderedCount = 15,
}: VirtualizedOrderCardProps) => {
  const itemId = `order-${order.id}`;

  return (
    <GridItem
      itemId={itemId}
      index={index}
      initialRenderedCount={initialRenderedCount}
    >
      <OrderCard
        animated={animated}
        order={order}
        collection={collection}
        onClick={onClick}
      />
    </GridItem>
  );
};
